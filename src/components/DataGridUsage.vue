<script setup lang="ts">
import DataGrid from '@/components/data-grid/core/DataGrid.vue'
import { useDataGrid } from '@/components/data-grid/composables/useDataGrid'
import { onMounted } from 'vue'

const dataGridInstance = useDataGrid('demo/demo', {
  enableSelection: 'checkbox',
  columns: [], // 将在 onMounted 中使用智能提示设置
  height: 'auto',
  toolbarOptions: {
    title: {
      main: 'DataGrid 使用示例',
      sub: '全面示范 DataGrid 的多种用法',
      icon: 'mdi:database',
      badge: {
        text: '使用示例',
        variant: 'default',
      },
    },
    queryParams: {
      limit: 20,
    },
    action: {
      create: true,
      bulkDelete: true,
    },
  },
  // tracePerformance: false, // 默认不开启性能监控
})

// 操作按钮配置 - 移到组件外部避免重复创建
const actionButtons = [
  {
    icon: 'mdi:pencil',
    text: '编辑',
    type: 'primary' as const,
    onClick: (row: any) => {
      console.log('编辑记录:', row.customer_name)
    },
  },
  {
    icon: 'mdi:delete',
    text: '删除',
    type: 'danger' as const,
    onClick: (row: any) => {
      console.log('删除记录:', row.customer_name)
    },
  },
  {
    icon: 'mdi:eye',
    text: '查看',
    type: 'info' as const,
    onClick: (row: any) => {
      console.log('查看记录:', row.customer_name)
    },
  },
]

const columnHelper = dataGridInstance.getColumnHelper()

onMounted(() => {
  const columns = [
    columnHelper.composite('customer_info', '客户信息', {
      main: { field: 'customer_name' },
      subs: {
        items: [
          {
            field: 'address',
            label: '地址',
            formatter(value: any, row: any) {
              return value ? `<i>${value}</i>` : ''
            },
          },
        ],
        layout: 'vertical',
      },
      icon: {
        type: 'avatar',
        avatarField: 'customer_name',
      },
      showActionsCount: 2,
      actions: actionButtons,
      fixed: 'left',
    }),

    // 邮箱列 - 专用邮箱Helper（演示阻止默认行为）
    columnHelper.mail('email', '邮箱', {
      width: 200,
      className: 'text-green-800',
      onClick: (row: any) => {
        console.log('🔍 自定义邮箱处理:', row.email)
        // 演示：如果邮箱包含 'test'，则阻止默认行为
        if (row.email.includes('test')) {
          alert('测试邮箱，无法发送邮件')
          return false // 阻止默认的 mailto: 行为
        }
        if (!row.email.includes('@')) {
          alert('邮箱格式不正确，无法发送邮件')
          return false // 阻止默认的 mailto: 行为
        }
        console.log('✅ 继续执行默认邮箱行为')
        return true // 继续默认行为
      },
    }),

    // 电话列 - 专用电话Helper（演示继续默认行为）
    columnHelper.phone('phone', '电话', {
      width: 160,
      className: 'text-red-800',
      onClick: (row: any) => {
        console.log('📞 记录电话点击事件:', row.phone)
        // 不返回值，继续执行默认的 tel: 行为
      },
    }),
    columnHelper.status('status', '状态', {
      variant: 'badge',
      width: 120,
      autoFromMetadata: true,
    }),

    // // 字符串渲染器示例
    // columnHelper.string('customer_name', '客户名称', {
    //   variant: 'text',
    //   fontStyle: 'bold',
    //   copyable: true,
    // }),

    // 关系列演示 - 多对一关系（销售员）- 使用 API 动态渲染
    columnHelper.relation('sales', '销售员', {
      displayField: 'nick_name',
      secondaryFields: ['employee_no'],
      variant: 'link',
      detailMode: 'dialog',
      moduleModel: 'hr/employee', // 使用 API 动态渲染
      apiDataKey: 'id', // 从关系数据中获取 ID 的字段名
      drawerSize: 'large',
      title: (data: any) =>
        `销售员详情 - ${data?.nick_name || data?.name || '未知'}`,
      onOpen: (data: any) => {
        console.log('打开销售员详情 (API 渲染):', data)
      },
      onClose: () => {
        console.log('关闭销售员详情')
      },
    }),

    // 代码列示例
    columnHelper.code('order_no', '订单号', {
      copyable: true,
    }),
    // 关系列演示 - 一对多关系（订单项）- 使用默认数据预览
    columnHelper.relationList('demo_items', '订单项', {
      displayField: 'product_name',
      maxDisplay: 1,
      overflowMode: 'expand',
      variant: 'badge',
      detailMode: 'drawer',
      dialogSize: 'large',
      moduleModel: 'demo/demo_item', // 使用 API 动态渲染
      title: '订单项详情',
      onOpen: (data: any) => {
        console.log('打开订单项详情 (默认预览):', data)
      },
      onClose: () => {
        console.log('关闭订单项详情')
      },
    }),

    columnHelper.currency('total_amount', '总金额', {
      variant: 'text',
      currencyField: 'currency', // 指定货币字段名，系统自动从该字段提取货币信息
      showDualCurrency: true,
      baseCurrency: 'CNY',
      dualCurrencyMode: 'primary-secondary',
      colors: {
        positiveColor: 'green-600',
        negativeColor: 'red-600',
        zeroColor: 'gray-500',
        defaultColor: 'blue-600',
      },
    }),

    // 使用优化的智能审计信息列替代分散的创建者/更新者列
    columnHelper.auditInfo('审计信息', {
      format: 'smart',
      dateFormat: {
        format: 'auto',
        showRelative: true,
        showSeconds: false,
        relativeThreshold: 7,
      },
      userDisplay: {
        format: 'username',
        enableUserQuery: true,
        unknownUserText: '未知用户',
      },
      theme: {
        fontSize: 'sm',
        spacing: 'comfortable',
        textColor: 'secondary',
      },
      width: 200,
    }),

    // 操作列 - 操作渲染器
    columnHelper.actions('操作', {
      width: 160,
      actions: actionButtons,
      fixed: 'right',
    }),
  ]
  dataGridInstance.gridOptions.value.columns = columns as any
  console.log(columnHelper)
})
</script>
<template>
  <DataGrid ref="dataGridRef" :data-grid-instance="dataGridInstance" />
</template>
