<template>
  <div class="flex items-center justify-center gap-1">
    <!-- 优化后的操作按钮 - 单一tooltip provider减少DOM节点 -->
    <TooltipProvider>
      <template
        v-for="action in directActions"
        :key="action.text || action.icon || action.tooltip"
      >
        <Tooltip v-if="action.tooltip || action.text">
          <TooltipTrigger as-child>
            <Button
              :variant="getButtonVariant(action)"
              :size="getButtonSize(action)"
              :disabled="isActionDisabled(action)"
              class="p-1 gap-0"
              @click="handleActionClick(action)"
            >
              <Icon v-show="action.icon" :icon="action.icon" class="w-4 h-4" />
              <span v-show="action.text">{{ action.text }}</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{{ action.tooltip || action.text }}</p>
          </TooltipContent>
        </Tooltip>
        <!-- 无tooltip的按钮直接渲染，减少DOM层级 -->
        <Button
          v-else
          :variant="getButtonVariant(action)"
          :size="getButtonSize(action)"
          :disabled="isActionDisabled(action)"
          class="p-1 gap-0"
          @click="handleActionClick(action)"
        >
          <Icon v-show="action.icon" :icon="action.icon" class="w-4 h-4" />
          <span v-show="action.text">{{ action.text }}</span>
        </Button>
      </template>
    </TooltipProvider>

    <!-- 更多操作下拉菜单 -->
    <DropdownMenu v-if="hasMoreActions">
      <DropdownMenuTrigger>
        <Button variant="outline" size="sm" class="p-1 gap-0">
          <Icon icon="mdi:dots-horizontal" class="w-4 h-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <!-- 优化：移除下拉菜单中的tooltip，因为菜单项本身已经显示文本 -->
        <DropdownMenuItem
          v-for="action in dropdownActions"
          :key="action.text || action.icon || action.tooltip"
          :disabled="isActionDisabled(action)"
          :class="cn(getDropdownItemClasses(action))"
          @select="handleActionClick(action)"
        >
          <div class="flex items-center w-full">
            <Icon
              v-show="action.icon"
              :icon="action.icon"
              class="w-4 h-4 mr-2"
            />
            <span>{{ action.text || action.tooltip || '操作' }}</span>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'ActionsRenderer',
})

import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { Icon } from '@iconify/vue'
import { cn } from '@/lib/utils'
import type { ActionsRendererProps } from './types'
import { useActionsRenderer } from './useRenderer'

const props = defineProps<ActionsRendererProps>()

const {
  directActions,
  dropdownActions,
  hasMoreActions,
  getButtonVariant,
  getDropdownItemClasses,
  getButtonSize,
  isActionDisabled,
  handleActionClick,
} = useActionsRenderer(props)
</script>
