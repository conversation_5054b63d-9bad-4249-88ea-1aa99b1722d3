<!-- 使用渲染函数避免模板中的条件渲染 -->

<script lang="ts">
import { h } from 'vue'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { Icon } from '@iconify/vue'
import { cn } from '@/lib/utils'
import type { ActionsRendererProps } from './types'
import { useActionsRenderer } from './useRenderer'

export default {
  name: 'ActionsRenderer',
  setup(props: ActionsRendererProps) {
    const {
      directActions,
      dropdownActions,
      hasMoreActions,
      getButtonVariant,
      getDropdownItemClasses,
      getButtonSize,
      isActionDisabled,
      handleActionClick,
    } = useActionsRenderer(props)

    return {
      directActions,
      dropdownActions,
      hasMoreActions,
      getButtonVariant,
      getDropdownItemClasses,
      getButtonSize,
      isActionDisabled,
      handleActionClick,
    }
  },
  render() {
    const children = []

    // 渲染直接显示的按钮
    if (this.directActions.length > 0) {
      const tooltipProvider = h(
        TooltipProvider,
        {},
        this.directActions.map((action) => {
          const buttonContent = []

          // 添加图标
          if (action.icon) {
            buttonContent.push(
              h(Icon, {
                icon: action.icon,
                class: 'w-4 h-4',
              })
            )
          }

          // 添加文本
          if (action.text) {
            buttonContent.push(h('span', action.text))
          }

          const button = h(
            Button,
            {
              variant: this.getButtonVariant(action),
              size: this.getButtonSize(action),
              disabled: this.isActionDisabled(action),
              class: 'p-1 gap-0',
              onClick: () => this.handleActionClick(action),
            },
            buttonContent
          )

          // 如果有tooltip，包装在Tooltip中
          if (action.tooltip || action.text) {
            return h(Tooltip, {}, [
              h(TooltipTrigger, { asChild: true }, button),
              h(TooltipContent, {}, h('p', action.tooltip || action.text)),
            ])
          }

          return button
        })
      )
      children.push(tooltipProvider)
    }

    // 渲染下拉菜单
    if (this.hasMoreActions) {
      const dropdownMenu = h(DropdownMenu, {}, [
        h(
          DropdownMenuTrigger,
          {},
          h(
            Button,
            {
              variant: 'outline',
              size: 'sm',
              class: 'p-1 gap-0',
            },
            h(Icon, {
              icon: 'mdi:dots-horizontal',
              class: 'w-4 h-4',
            })
          )
        ),
        h(
          DropdownMenuContent,
          { align: 'end' },
          this.dropdownActions.map((action) => {
            const itemContent = []

            if (action.icon) {
              itemContent.push(
                h(Icon, {
                  icon: action.icon,
                  class: 'w-4 h-4 mr-2',
                })
              )
            }

            itemContent.push(h('span', action.text || action.tooltip || '操作'))

            return h(
              DropdownMenuItem,
              {
                disabled: this.isActionDisabled(action),
                class: cn(this.getDropdownItemClasses(action)),
                onSelect: () => this.handleActionClick(action),
              },
              h('div', { class: 'flex items-center w-full' }, itemContent)
            )
          })
        ),
      ])
      children.push(dropdownMenu)
    }

    return h(
      'div',
      { class: 'flex items-center justify-center gap-1' },
      children
    )
  },
}
</script>
