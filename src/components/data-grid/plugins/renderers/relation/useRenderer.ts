/**
 * 关系渲染器业务逻辑钩子
 *
 * 提供关系数据处理、配置合并、交互处理等核心逻辑
 */

import { computed } from 'vue'
import type { RelationRendererProps } from './types'
import {
  formatRelationDisplay,
  getRelationType,
  inferDisplayField,
  buildSecondaryInfo,
} from './utils'

// 静态配置对象 - 移到模块外部
const SIZE_MAP = {
  mini: '!w-[80vw] !max-w-[400px]',
  small: '!w-[80vw] !max-w-[600px]',
  middle: '!w-[80vw] !max-w-[800px]',
  large: '!w-[70vw] !max-w-[1200px]',
} as const

// 组件导入缓存
const componentCache = new Map<string, any>()

// 缓存组件导入
const getCachedComponent = async (
  componentName: string,
  importFn: () => Promise<any>
) => {
  if (componentCache.has(componentName)) {
    return componentCache.get(componentName)
  }

  const component = await importFn()
  componentCache.set(componentName, component)
  return component
}
export const useRelationRenderer = (props: RelationRendererProps) => {
  // 获取关系数据 - 保持与其他渲染器一致的逻辑
  const relationData = computed(() => {
    return props.field ? props.row?.[props.field] : props.value
  })

  // 关系类型判断
  const relationType = computed(() => {
    return getRelationType(relationData.value)
  })

  // 配置合并 - 提供合理的默认值
  const finalConfig = computed(() => ({
    variant: 'text' as const,
    autoFromMetadata: true,
    maxDisplay: 2,
    overflowMode: 'tooltip' as const,
    detailMode: 'drawer' as const,
    errorText: '--',
    ...props.config,
  }))

  // 显示字段推断
  const displayField = computed(() => {
    if (finalConfig.value.displayField) {
      return finalConfig.value.displayField
    }

    // 自动推断显示字段
    if (finalConfig.value.autoFromMetadata && relationData.value) {
      const sampleData = Array.isArray(relationData.value)
        ? relationData.value[0]
        : relationData.value
      return inferDisplayField(sampleData)
    }

    return null
  })

  // 主显示文本
  const displayText = computed(() => {
    try {
      return formatRelationDisplay(relationData.value, finalConfig.value)
    } catch (error) {
      console.error('关系渲染器显示文本格式化失败:', error)
      return finalConfig.value.errorText || '--'
    }
  })

  // 是否有数据
  const hasData = computed(() => {
    if (Array.isArray(relationData.value)) {
      return relationData.value.length > 0
    }
    return relationData.value != null
  })

  // 是否有更多项目（用于一对多溢出显示）
  const hasMore = computed(() => {
    if (
      relationType.value === 'ONETOMANY' &&
      Array.isArray(relationData.value)
    ) {
      const maxDisplay = finalConfig.value.maxDisplay || 2
      return relationData.value.length > maxDisplay
    }
    return false
  })

  // 剩余项目数量
  const remainingCount = computed(() => {
    if (
      relationType.value === 'ONETOMANY' &&
      Array.isArray(relationData.value)
    ) {
      const maxDisplay = finalConfig.value.maxDisplay || 2
      return Math.max(0, relationData.value.length - maxDisplay)
    }
    return 0
  })

  // 显示的项目列表（用于一对多）
  const displayItems = computed(() => {
    if (
      relationType.value === 'ONETOMANY' &&
      Array.isArray(relationData.value)
    ) {
      const maxDisplay = finalConfig.value.maxDisplay || 2
      return relationData.value.slice(0, maxDisplay)
    }
    return []
  })

  // 单个项目显示文本（用于多对一）
  const singleItemText = computed(() => {
    if (relationType.value === 'MANYTOONE' && relationData.value) {
      return getItemDisplayText(relationData.value)
    }
    return '--'
  })

  // 次要信息（如员工编号等）
  const secondaryInfo = computed(() => {
    const data = relationType.value === 'MANYTOONE' ? relationData.value : null

    if (data && finalConfig.value.secondaryFields) {
      return buildSecondaryInfo(data, finalConfig.value.secondaryFields)
    }
    return []
  })

  // 获取单个项目的显示文本
  const getItemDisplayText = (item: any): string => {
    if (!item || typeof item !== 'object') {
      return String(item || '--')
    }

    const field = displayField.value
    if (field && item[field]) {
      return String(item[field])
    }

    // 使用推断的显示字段
    const inferred = inferDisplayField(item)
    if (inferred && item[inferred]) {
      return String(item[inferred])
    }

    return item.id ? `项目 (ID: ${item.id})` : '未知项目'
  }

  // 公共的应用创建逻辑
  const createModalApp = async (
    data: any,
    config: any,
    renderFn: (components: any[], title: string) => any
  ) => {
    const { createApp, h, ref } = await import('vue')

    const title =
      typeof config.title === 'function'
        ? config.title(data)
        : config.title || '关联详情'

    // 创建容器
    const container = document.createElement('div')
    document.body.appendChild(container)

    // 创建应用实例
    const app = createApp({
      setup() {
        const visible = ref(true)

        const handleClose = () => {
          visible.value = false
          setTimeout(() => {
            app.unmount()
            document.body.removeChild(container)
            if (config.onClose) {
              config.onClose()
            }
          }, 300)
        }

        return {
          visible,
          handleClose,
          data,
          config,
          row: props.row,
          title,
        }
      },
      render: renderFn,
      methods: {
        getSizeClass(size: string) {
          return SIZE_MAP[size as keyof typeof SIZE_MAP] || SIZE_MAP.middle
        },
      },
    })

    app.mount(container)
  }

  // 点击处理 - 简化版本
  const handleClick = (data: any) => {
    if (finalConfig.value.onClick) {
      const result = finalConfig.value.onClick(data, props.row)
      if (result === false) return
    }
    openDetail(data)
  }

  // 打开详情 - 简化调用链
  const openDetail = async (data: any) => {
    if (!data) {
      console.warn('无法打开详情：缺少有效的项目数据')
      return
    }

    try {
      const mode = finalConfig.value.detailMode || 'drawer'
      const config = finalConfig.value

      switch (mode) {
        case 'drawer':
          await openDrawerDetail(data, config)
          break
        case 'dialog':
          await openDialogDetail(data, config)
          break
        case 'page':
          await openPageDetail(data, config)
          break
      }
    } catch (error) {
      console.error('打开详情失败:', error)
    }
  }

  // 使用抽屉显示详情 - 简化版本
  const openDrawerDetail = async (data: any, config: any) => {
    await openCustomDrawer(data, config)
  }

  // 自定义抽屉实现 - 使用组件缓存
  const openCustomDrawer = async (data: any, config: any) => {
    const { createApp, h, ref } = await import('vue')

    // 使用缓存的组件导入
    const [SideDrawer, SheetTitle, SheetDescription, RelationDetailViewer] =
      await Promise.all([
        getCachedComponent('SideDrawer', () =>
          import('@/components/common/SideDrawer.vue').then((m) => m.default)
        ),
        getCachedComponent('SheetTitle', () =>
          import('@/components/ui/sheet').then((m) => m.SheetTitle)
        ),
        getCachedComponent('SheetDescription', () =>
          import('@/components/ui/sheet').then((m) => m.SheetDescription)
        ),
        getCachedComponent('RelationDetailViewer', () =>
          import('./RelationDetailViewer.vue').then((m) => m.default)
        ),
      ])

    const title =
      typeof config.title === 'function'
        ? config.title(data)
        : config.title || '关联详情'

    // 创建抽屉容器
    const drawerContainer = document.createElement('div')
    document.body.appendChild(drawerContainer)

    // 创建 Vue 应用实例
    const app = createApp({
      setup() {
        const visible = ref(true)

        const handleClose = () => {
          visible.value = false
          // 延迟销毁，等待动画完成
          setTimeout(() => {
            app.unmount()
            document.body.removeChild(drawerContainer)
            if (config.onClose) {
              config.onClose()
            }
          }, 300)
        }

        return {
          visible,
          handleClose,
          data,
          config,
          row: props.row,
          title,
        }
      },
      render() {
        return h(
          SideDrawer,
          {
            modelValue: this.visible,
            'onUpdate:modelValue': this.handleClose,
            contentClass: this.getSizeClass(config.drawerSize || 'middle'),
          },
          {
            header: () =>
              h('div', { class: 'p-4 border-b' }, [
                h(
                  SheetTitle,
                  { class: 'text-lg font-semibold' },
                  {
                    default: () => this.title,
                  }
                ),
                h(
                  SheetDescription,
                  { class: 'text-sm text-muted-foreground mt-1' },
                  {
                    default: () => '查看关联数据详情',
                  }
                ),
              ]),
            default: () =>
              h(RelationDetailViewer, {
                relationData: this.data,
                row: this.row,
                config: this.config,
              }),
          }
        )
      },
      methods: {
        getSizeClass(size: string) {
          return SIZE_MAP[size as keyof typeof SIZE_MAP] || SIZE_MAP.middle
        },
      },
    })

    app.mount(drawerContainer)
  }

  // 使用对话框显示详情
  const openDialogDetail = async (data: any, config: any) => {
    // 使用自定义对话框实现
    await openCustomDialog(data, config)
  }

  // 自定义对话框实现 - 使用组件缓存
  const openCustomDialog = async (data: any, config: any) => {
    const { createApp, h, ref } = await import('vue')

    // 使用缓存的组件导入
    const [
      Dialog,
      DialogContent,
      DialogHeader,
      DialogTitle,
      DialogDescription,
      RelationDetailViewer,
    ] = await Promise.all([
      getCachedComponent('Dialog', () =>
        import('@/components/ui/dialog').then((m) => m.Dialog)
      ),
      getCachedComponent('DialogContent', () =>
        import('@/components/ui/dialog').then((m) => m.DialogContent)
      ),
      getCachedComponent('DialogHeader', () =>
        import('@/components/ui/dialog').then((m) => m.DialogHeader)
      ),
      getCachedComponent('DialogTitle', () =>
        import('@/components/ui/dialog').then((m) => m.DialogTitle)
      ),
      getCachedComponent('DialogDescription', () =>
        import('@/components/ui/dialog').then((m) => m.DialogDescription)
      ),
      getCachedComponent('RelationDetailViewer', () =>
        import('./RelationDetailViewer.vue').then((m) => m.default)
      ),
    ])

    const title =
      typeof config.title === 'function'
        ? config.title(data)
        : config.title || '关联详情'

    // 创建对话框容器
    const dialogContainer = document.createElement('div')
    document.body.appendChild(dialogContainer)

    // 创建 Vue 应用实例
    const app = createApp({
      setup() {
        const visible = ref(true)

        const handleClose = () => {
          visible.value = false
          // 延迟销毁，等待动画完成
          setTimeout(() => {
            app.unmount()
            document.body.removeChild(dialogContainer)
            if (config.onClose) {
              config.onClose()
            }
          }, 300)
        }

        return {
          visible,
          handleClose,
          data,
          config,
          row: props.row,
          title,
        }
      },
      render() {
        return h(
          Dialog,
          {
            open: this.visible,
            'onUpdate:open': this.handleClose,
          },
          {
            default: () =>
              h(
                DialogContent,
                {
                  class: this.getSizeClass(config.dialogSize || 'middle'),
                },
                {
                  default: () => [
                    h(
                      DialogHeader,
                      {},
                      {
                        default: () => [
                          h(
                            DialogTitle,
                            {},
                            {
                              default: () => this.title,
                            }
                          ),
                          h(
                            DialogDescription,
                            {},
                            {
                              default: () => '查看关联数据详情',
                            }
                          ),
                        ],
                      }
                    ),
                    h('div', { class: 'max-h-[80vh] overflow-y-auto p-4' }, [
                      h(RelationDetailViewer, {
                        relationData: this.data,
                        row: this.row,
                        config: this.config,
                      }),
                    ]),
                  ],
                }
              ),
          }
        )
      },
      methods: {
        getSizeClass(size: string) {
          return SIZE_MAP[size as keyof typeof SIZE_MAP] || SIZE_MAP.middle
        },
      },
    })

    app.mount(dialogContainer)
  }

  // 跳转到详情页面
  const openPageDetail = async (data: any, config: any) => {
    if (!config.routePath) {
      console.warn('页面模式需要配置 routePath')
      return
    }

    const { useRouter } = await import('vue-router')
    const router = useRouter()

    let path = ''
    if (typeof config.routePath === 'function') {
      path = config.routePath(data)
    } else {
      path = config.routePath
    }

    // 构建路由参数
    const routeParams: any = { path }

    if (config.routeQuery) {
      const query =
        typeof config.routeQuery === 'function'
          ? config.routeQuery(data)
          : config.routeQuery
      routeParams.query = query
    }

    // 触发打开回调
    if (config.onOpen) {
      config.onOpen(data)
    }

    router.push(routeParams)
  }

  // 样式相关的计算属性
  const variantClasses = computed(() => {
    const variant = finalConfig.value.variant

    switch (variant) {
      case 'badge':
        return 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800'
      case 'link':
        return 'text-blue-600 hover:text-blue-800 cursor-pointer underline'
      case 'avatar':
        return 'flex items-center space-x-2'
      case 'text':
      default:
        return 'text-gray-900'
    }
  })

  return {
    // 数据
    relationData,
    relationType,
    displayText,
    singleItemText,
    displayItems,
    secondaryInfo,

    // 状态
    hasData,
    hasMore,
    remainingCount,

    // 配置
    finalConfig,
    displayField,

    // 方法
    handleClick,
    getItemDisplayText,

    // 样式
    variantClasses,
  }
}
