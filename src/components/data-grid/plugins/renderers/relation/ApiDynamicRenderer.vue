<template>
  <div class="api-dynamic-renderer">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center p-8">
      <div
        class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
      ></div>
      <span class="ml-2 text-gray-600">正在加载数据...</span>
    </div>

    <!-- 错误状态 -->
    <div
      v-else-if="error"
      class="p-4 bg-red-50 border border-red-200 rounded-lg"
    >
      <div class="flex items-center">
        <svg
          class="w-5 h-5 text-red-500 mr-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          ></path>
        </svg>
        <h3 class="text-red-800 font-medium">数据加载失败</h3>
      </div>
      <p class="text-red-700 mt-1">{{ error }}</p>
      <button
        @click="retryLoad"
        class="mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
      >
        重试
      </button>
    </div>

    <!-- 数据展示 -->
    <div v-else-if="detailData" class="api-detail-content">
      <!-- 标题区域 -->
      <!-- <div class="mb-6 pb-4 border-b border-gray-200">
        <h2 class="text-xl font-semibold text-gray-900">
          {{ getTitle() }}
        </h2>
        <p class="text-sm text-gray-500 mt-1">
          {{ moduleModel }} - {{ getSubtitle() }}
        </p>
      </div> -->

      <!-- 数组数据展示 -->
      <div v-if="Array.isArray(detailData)" class="space-y-4">
        <h3 class="text-lg font-medium text-gray-800">
          数据列表 ({{ detailData.length }} 项)
        </h3>
        <div class="space-y-3">
          <div
            v-for="(item, index) in detailData"
            :key="index"
            class="bg-gray-50 rounded-lg p-4 border"
          >
            <div class="flex justify-between items-center mb-2">
              <h4 class="font-medium text-gray-700">项目 {{ index + 1 }}</h4>
              <span class="text-sm text-gray-500"
                >ID: {{ item.id || '--' }}</span
              >
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div
                v-for="(value, key) in getDisplayFields(item)"
                :key="key"
                class="flex justify-between items-start py-1"
              >
                <span
                  class="font-medium text-gray-600 text-sm min-w-0 flex-shrink-0 mr-3"
                >
                  {{ getFieldLabel(key) }}:
                </span>
                <div class="text-right min-w-0 flex-1 text-sm">
                  <DynamicFieldRenderer
                    :value="value"
                    :field-info="getFieldInfo(key)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 单个对象数据展示 -->
      <div v-else class="space-y-6">
        <div
          v-for="group in fieldGroups"
          :key="group.name"
          class="bg-gray-50 rounded-lg p-4"
        >
          <h3 class="text-lg font-medium text-gray-800 mb-3">
            {{ group.label }}
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div
              v-for="field in group.fields"
              :key="field.name"
              class="flex justify-between items-start py-2"
            >
              <span
                class="font-medium text-gray-600 min-w-0 flex-shrink-0 mr-4"
              >
                {{ field.comment || field.name }}:
              </span>
              <div class="text-right min-w-0 flex-1">
                <DynamicFieldRenderer
                  :value="detailData[field.name]"
                  :field-info="field"
                  :field-type="getUserFieldType(field.name)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 关系数据展示 -->
      <div v-if="relationFields.length > 0" class="mt-6 space-y-4">
        <h3 class="text-lg font-medium text-gray-800">关联信息</h3>
        <div class="grid grid-cols-1 gap-4">
          <div
            v-for="field in relationFields"
            :key="field.name"
            class="bg-blue-50 rounded-lg p-4"
          >
            <h4 class="font-medium text-blue-800 mb-2">
              {{ field.label || field.name }}
            </h4>
            <DynamicFieldRenderer
              :value="detailData[field.name]"
              :field-info="field"
              field-type="relation"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 空数据状态 -->
    <div v-else class="text-center py-8">
      <svg
        class="w-12 h-12 text-gray-400 mx-auto mb-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
        ></path>
      </svg>
      <p class="text-gray-500">暂无数据</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { getApi } from '@/api/apiService'
import type { ModelApi } from '@/api/apiService'
import DynamicFieldRenderer from './DynamicFieldRenderer.vue'

interface Props {
  moduleModel: string
  relationData: any
  apiDataKey?: string
  config: any
}

const props = defineProps<Props>()

const loading = ref(false)
const error = ref<string>('')
const detailData = ref<any>(null)
const metadata = ref<any>(null)

// 静态配置 - 移到组件外部
const EXCLUDE_FIELDS = new Set([
  'created_at',
  'updated_at',
  'deleted_at',
  'password',
  'token',
])

// 字段分组配置
const FIELD_GROUP_RULES = [
  {
    name: 'contact',
    label: '联系信息',
    patterns: ['email', 'phone', 'address'],
  },
  {
    name: 'business',
    label: '业务信息',
    patterns: ['amount', 'price', 'cost', 'sales', 'revenue'],
  },
  {
    name: 'system',
    label: '系统信息',
    patterns: ['created', 'updated', 'modified', 'status'],
  },
  {
    name: 'basic',
    label: '基本信息',
    patterns: ['name', 'title', 'code', 'no'],
  },
] as const

// 获取数据 ID
const getDataId = () => {
  const key = props.apiDataKey || 'id'
  return props.relationData?.[key] || props.relationData?.id
}

// 加载数据 - 优化版本，移除调试日志
const loadData = async () => {
  const dataId = getDataId()

  if (!dataId) {
    error.value = '缺少数据 ID，无法获取详情'
    return
  }

  loading.value = true
  error.value = ''

  try {
    // 获取 API 实例
    const api: ModelApi = await getApi(props.moduleModel)

    // 对于数组数据，直接使用关系数据，不调用 getDetail
    if (Array.isArray(props.relationData)) {
      const metadataResponse = await api.getMetadata()
      metadata.value = metadataResponse
      detailData.value = props.relationData
      return
    }

    // 并行获取元数据和详情数据
    const [metadataResponse, detailResponse] = await Promise.all([
      api.getMetadata(),
      api.getDetail
        ? api.getDetail(dataId)
        : Promise.resolve(props.relationData),
    ])

    metadata.value = metadataResponse
    detailData.value = detailResponse
  } catch (err: any) {
    error.value = err.message || '数据加载失败'
  } finally {
    loading.value = false
  }
}

// 重试加载
const retryLoad = () => {
  loadData()
}

// 这些函数暂时保留，可能在模板中使用
// const getTitle = () => {
//   if (!detailData.value) return '详情'
//   const titleFields = ['name', 'title', 'nick_name', 'customer_name', 'product_name', 'order_no']
//   for (const field of titleFields) {
//     if (detailData.value[field]) {
//       return detailData.value[field]
//     }
//   }
//   return `${props.moduleModel} 详情`
// }

// const getSubtitle = () => {
//   if (!detailData.value) return ''
//   const id = detailData.value.id
//   const code = detailData.value.code || detailData.value.employee_no || detailData.value.order_no
//   if (code && id) return `${code} (ID: ${id})`
//   else if (code) return code
//   else if (id) return `ID: ${id}`
//   return ''
// }

// 字段分组 - 优化版本
const fieldGroups = computed(() => {
  if (!metadata.value?.fields || !detailData.value) return []

  // 初始化分组
  const groups = new Map([
    ['basic', { name: 'basic', label: '基本信息', fields: [] as any[] }],
    ['contact', { name: 'contact', label: '联系信息', fields: [] as any[] }],
    ['business', { name: 'business', label: '业务信息', fields: [] as any[] }],
    ['system', { name: 'system', label: '系统信息', fields: [] as any[] }],
    ['other', { name: 'other', label: '其他信息', fields: [] as any[] }],
  ])

  // 分类字段
  metadata.value.fields.forEach((field: any) => {
    // 跳过不需要显示的字段
    if (
      field.name === 'id' ||
      field.name.endsWith('_id') ||
      field.type === 'relation' ||
      !(field.name in detailData.value)
    ) {
      return
    }

    const fieldName = field.name.toLowerCase()
    let groupName = 'other' // 默认分组

    // 使用预定义规则进行分组
    for (const rule of FIELD_GROUP_RULES) {
      if (
        rule.patterns.some((pattern: string) => fieldName.includes(pattern))
      ) {
        groupName = rule.name
        break
      }
    }

    groups.get(groupName)?.fields.push(field)
  })

  // 返回有字段的分组
  return Array.from(groups.values()).filter((group) => group.fields.length > 0)
})

// 关系字段
const relationFields = computed(() => {
  if (!metadata.value?.fields) return []

  return metadata.value.fields.filter(
    (field: any) =>
      field.type === 'relation' && field.name in (detailData.value || {})
  )
})

// 获取要显示的字段 - 优化版本
const getDisplayFields = (data: any) => {
  if (!data || typeof data !== 'object') return {}

  const result: Record<string, any> = {}

  Object.entries(data).forEach(([key, value]) => {
    if (!EXCLUDE_FIELDS.has(key) && value !== null && value !== undefined) {
      result[key] = value
    }
  })

  return result
}

// 获取字段标签
const getFieldLabel = (fieldName: string) => {
  // 尝试从元数据中获取字段标签
  if (metadata.value?.fields) {
    const field = metadata.value.fields.find((f: any) => f.name === fieldName)
    if (field) {
      return field.comment || field.label || field.name
    }
  }

  // 格式化字段名作为标签
  return fieldName.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())
}

// 获取字段信息
const getFieldInfo = (fieldName: string) => {
  if (metadata.value?.fields) {
    return (
      metadata.value.fields.find((f: any) => f.name === fieldName) || {
        name: fieldName,
      }
    )
  }
  return { name: fieldName }
}

// 获取用户字段类型
const getUserFieldType = (fieldName: string) => {
  const lowerFieldName = fieldName.toLowerCase()

  // 检查是否为用户字段
  if (
    lowerFieldName === 'created_by' ||
    lowerFieldName === 'updated_by' ||
    lowerFieldName.includes('created_by') ||
    lowerFieldName.includes('updated_by') ||
    lowerFieldName.includes('author_id') ||
    lowerFieldName.includes('user_id') ||
    lowerFieldName.includes('creator') ||
    lowerFieldName.includes('modifier') ||
    lowerFieldName.endsWith('_by')
  ) {
    return 'user'
  }

  return undefined // 让 DynamicFieldRenderer 自己推断
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.api-dynamic-renderer {
  max-width: 800px;
  margin: 0 auto;
}

.api-detail-content {
  padding: 0;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
