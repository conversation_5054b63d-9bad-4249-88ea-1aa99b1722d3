<template>
  <div class="dynamic-field-renderer">
    <!-- 文本类型 -->
    <span v-if="fieldType === 'text'" class="text-gray-900">
      {{ displayValue }}
    </span>

    <!-- 数字类型 -->
    <span v-else-if="fieldType === 'number'" class="text-gray-900 font-mono">
      {{ formatNumber(value) }}
    </span>

    <!-- 货币类型 -->
    <span
      v-else-if="fieldType === 'currency'"
      class="text-green-600 font-semibold"
    >
      {{ formatCurrency(value) }}
    </span>

    <!-- 日期类型 -->
    <span v-else-if="fieldType === 'date'" class="text-blue-600">
      {{ formatDate(value) }}
    </span>

    <!-- 日期时间类型 -->
    <span v-else-if="fieldType === 'datetime'" class="text-blue-600">
      {{ formatDateTime(value) }}
    </span>

    <!-- 布尔类型 -->
    <span v-else-if="fieldType === 'boolean'" class="inline-flex items-center">
      <span
        :class="[
          'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
          value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800',
        ]"
      >
        <svg
          :class="['w-3 h-3 mr-1', value ? 'text-green-500' : 'text-red-500']"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            v-if="value"
            fill-rule="evenodd"
            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
            clip-rule="evenodd"
          />
          <path
            v-else
            fill-rule="evenodd"
            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
            clip-rule="evenodd"
          />
        </svg>
        {{ value ? '是' : '否' }}
      </span>
    </span>

    <!-- 枚举类型 -->
    <span v-else-if="fieldType === 'enum'" class="inline-flex items-center">
      <span
        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
      >
        {{ getEnumDisplayValue(value) }}
      </span>
    </span>

    <!-- 状态类型 -->
    <span v-else-if="fieldType === 'status'" class="inline-flex items-center">
      <span
        :class="[
          'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
          getStatusClass(value),
        ]"
      >
        <span
          :class="['w-2 h-2 rounded-full mr-1', getStatusDotClass(value)]"
        ></span>
        {{ getStatusDisplayValue(value) }}
      </span>
    </span>

    <!-- 邮箱类型 -->
    <a
      v-else-if="fieldType === 'email'"
      :href="`mailto:${value}`"
      class="text-blue-600 hover:text-blue-800 underline"
    >
      {{ value }}
    </a>

    <!-- 电话类型 -->
    <a
      v-else-if="fieldType === 'phone'"
      :href="`tel:${value}`"
      class="text-green-600 hover:text-green-800 underline"
    >
      {{ value }}
    </a>

    <!-- URL 类型 -->
    <a
      v-else-if="fieldType === 'url'"
      :href="value"
      target="_blank"
      rel="noopener noreferrer"
      class="text-blue-600 hover:text-blue-800 underline"
    >
      {{ value }}
    </a>

    <!-- 用户类型 -->
    <span v-else-if="fieldType === 'user'" class="inline-flex items-center">
      <span v-if="userLoading" class="text-gray-500 text-sm">
        <svg
          class="w-3 h-3 animate-spin mr-1"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          ></path>
        </svg>
        加载中...
      </span>
      <span v-else-if="userError" class="text-red-500 text-sm">
        ID: {{ value }}
      </span>
      <span v-else-if="userName" class="text-blue-600 font-medium">
        {{ userName }}
      </span>
      <span v-else class="text-gray-500"> ID: {{ value }} </span>
    </span>

    <!-- 关系类型 -->
    <span v-else-if="fieldType === 'relation'" class="text-purple-600">
      {{ getRelationDisplayValue(value) }}
    </span>

    <!-- 默认类型 -->
    <span v-else class="text-gray-900">
      {{ displayValue }}
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import userCache from '@/utils/userCache'

interface Props {
  value: any
  fieldInfo: any
  fieldType?: string
}

const props = defineProps<Props>()

// 用户名缓存
const userName = ref<string | null>(null)
const userLoading = ref(false)
const userError = ref(false)

// 静态映射对象 - 移到组件外部避免重复创建
const STATUS_CONFIG = {
  active: {
    label: '活跃',
    class: 'bg-green-100 text-green-800',
    dot: 'bg-green-500',
  },
  inactive: {
    label: '非活跃',
    class: 'bg-gray-100 text-gray-800',
    dot: 'bg-gray-500',
  },
  pending: {
    label: '待处理',
    class: 'bg-yellow-100 text-yellow-800',
    dot: 'bg-yellow-500',
  },
  completed: {
    label: '已完成',
    class: 'bg-blue-100 text-blue-800',
    dot: 'bg-blue-500',
  },
  cancelled: {
    label: '已取消',
    class: 'bg-red-100 text-red-800',
    dot: 'bg-red-500',
  },
  draft: {
    label: '草稿',
    class: 'bg-gray-100 text-gray-800',
    dot: 'bg-gray-500',
  },
  published: {
    label: '已发布',
    class: 'bg-green-100 text-green-800',
    dot: 'bg-green-500',
  },
  archived: {
    label: '已归档',
    class: 'bg-purple-100 text-purple-800',
    dot: 'bg-purple-500',
  },
} as const

// 字段类型推断规则
const FIELD_TYPE_RULES = [
  { pattern: /email/i, type: 'email' },
  { pattern: /(phone|tel)/i, type: 'phone' },
  { pattern: /(url|link)/i, type: 'url' },
  { pattern: /status/i, type: 'status' },
  { pattern: /(amount|price|cost)/i, type: 'currency' },
  { pattern: /time/i, type: 'datetime' },
  { pattern: /date/i, type: 'date' },
] as const

// 计算显示值
const displayValue = computed(() => {
  if (props.value === null || props.value === undefined) {
    return '--'
  }
  return String(props.value)
})

// 推断字段类型 - 优化版本
const fieldType = computed(() => {
  if (props.fieldType) return props.fieldType

  const fieldInfo = props.fieldInfo
  if (!fieldInfo) return 'text'

  // 根据字段信息推断类型
  if (fieldInfo.type) {
    const typeMap: Record<string, string> = {
      integer: 'number',
      float: 'number',
      decimal: 'number',
      boolean: 'boolean',
      timestamp: 'datetime',
      enum: 'enum',
      relation: 'relation',
    }
    const mappedType = typeMap[fieldInfo.type.toLowerCase()]
    if (mappedType) return mappedType
  }

  // 根据字段名推断类型
  const fieldName = fieldInfo.name?.toLowerCase() || ''

  // 用户字段检测
  if (fieldName.endsWith('_by')) {
    return 'user'
  }

  // 使用预定义规则进行匹配
  for (const rule of FIELD_TYPE_RULES) {
    if (rule.pattern.test(fieldName)) {
      return rule.type
    }
  }

  return 'text'
})

// 公共的空值检查函数
const isEmptyValue = (value: any): boolean => {
  return value === null || value === undefined || value === ''
}

// 格式化数字
const formatNumber = (value: any) => {
  if (isEmptyValue(value)) return '--'
  const num = Number(value)
  if (isNaN(num)) return value
  return new Intl.NumberFormat('zh-CN').format(num)
}

// 格式化货币
const formatCurrency = (value: any) => {
  if (isEmptyValue(value)) return '--'
  const num = Number(value)
  if (isNaN(num)) return value
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
  }).format(num)
}

// 格式化日期 - 优化版本
const formatDate = (value: any) => {
  if (isEmptyValue(value)) return '--'
  try {
    const date = new Date(value)
    if (isNaN(date.getTime())) return value
    return date.toLocaleDateString('zh-CN')
  } catch {
    return value
  }
}

// 格式化日期时间 - 优化版本
const formatDateTime = (value: any) => {
  if (isEmptyValue(value)) return '--'
  try {
    const date = new Date(value)
    if (isNaN(date.getTime())) return value
    return date.toLocaleString('zh-CN')
  } catch {
    return value
  }
}

// 获取枚举显示值
const getEnumDisplayValue = (value: any) => {
  const enumInfo = props.fieldInfo?.enum_info
  if (enumInfo && enumInfo.enum_values && enumInfo.enum_values[value]) {
    return enumInfo.enum_values[value]
  }
  return value || '--'
}

// 获取状态显示值 - 使用预定义配置
const getStatusDisplayValue = (value: any) => {
  const config = STATUS_CONFIG[value as keyof typeof STATUS_CONFIG]
  return config?.label || value || '--'
}

// 获取状态样式类 - 使用预定义配置
const getStatusClass = (value: any) => {
  const config = STATUS_CONFIG[value as keyof typeof STATUS_CONFIG]
  return config?.class || 'bg-gray-100 text-gray-800'
}

// 获取状态点样式类 - 使用预定义配置
const getStatusDotClass = (value: any) => {
  const config = STATUS_CONFIG[value as keyof typeof STATUS_CONFIG]
  return config?.dot || 'bg-gray-500'
}

// 获取关系显示值
const getRelationDisplayValue = (value: any) => {
  if (!value) return '--'
  if (typeof value === 'object') {
    return (
      value.name ||
      value.title ||
      value.nick_name ||
      value.code ||
      value.id ||
      '--'
    )
  }
  return value
}

// 加载用户信息
const loadUserInfo = async (userId: number | string) => {
  if (!userId || userId === null || userId === undefined) {
    userName.value = null
    return
  }

  userLoading.value = true
  userError.value = false
  userName.value = null

  try {
    // 获取用户名
    const username = await userCache.getUserName(userId)

    if (username) {
      userName.value = username
    } else {
      userName.value = null
      userError.value = true
    }
  } catch (error) {
    console.warn('加载用户信息失败:', error)
    userName.value = null
    userError.value = true
  } finally {
    userLoading.value = false
  }
}

// 监听用户字段的值变化 - 优化版本，移除重复的初始化逻辑
watch(
  () => [props.value, fieldType.value],
  ([newValue, newFieldType]) => {
    if (newFieldType === 'user' && newValue) {
      loadUserInfo(newValue)
    } else {
      // 重置用户相关状态
      userName.value = null
      userLoading.value = false
      userError.value = false
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.dynamic-field-renderer {
  display: inline-flex;
  align-items: center;
}
</style>
