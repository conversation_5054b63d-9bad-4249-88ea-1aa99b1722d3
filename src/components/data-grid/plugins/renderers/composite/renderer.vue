<template>
  <TooltipProvider>
    <div
      ref="containerRef"
      :class="containerClasses"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
    >
      <!-- 图标区域 - 统一组件渲染 -->
      <component
        v-if="iconComponent"
        :is="components[iconComponent.type] || iconComponent.type"
        v-bind="iconComponent.props"
        :class="iconComponent.class"
      />

      <!-- 内容区域 -->
      <div class="flex-1 min-w-0">
        <!-- 主要内容 -->
        <div
          :class="
            cn(
              'truncate font-medium text-foreground',
              (config.main as any)?.className
            )
          "
          :style="(config.main as any)?.style"
        >
          <span v-if="mainContent.isHtml" v-html="mainContent.content"></span>
          <span v-else>{{ mainContent.content }}</span>
        </div>

        <!-- 子内容区域 -->
        <div v-if="subContents.hasItems" :class="subContentClasses">
          <template
            v-for="(sub, index) in subContents.items"
            :key="`sub-${index}-${sub.field}`"
          >
            <span :class="cn('truncate overflow-hidden', sub.className)">
              <span v-if="sub.isHtml" v-html="sub.content"></span>
              <span v-else>{{ sub.content }}</span>
            </span>
            <span
              v-if="
                subContents.layout === 'horizontal' &&
                index < subContents.items.length - 1
              "
              class="text-muted-foreground/60 mx-1"
            >
              {{ subContents.separator }}
            </span>
          </template>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div v-if="actions.shouldRender" :class="actionsClasses">
        <!-- 直接显示的操作按钮 -->
        <Tooltip
          v-for="action in actions.direct"
          :key="`action-${action.key || action.icon}`"
        >
          <TooltipTrigger as-child>
            <Button
              :variant="getButtonVariant(action)"
              size="sm"
              class="h-6 w-6 p-0 hover:scale-110 transition-transform"
              @click.stop="handleActionClick(action)"
            >
              <Icon :icon="action.icon" class="h-3 w-3" />
              <span class="sr-only">{{ action.tooltip || action.text }}</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{{ action.tooltip || action.text }}</p>
          </TooltipContent>
        </Tooltip>

        <!-- 下拉菜单 -->
        <DropdownMenu
          v-if="actions.dropdown.length > 0"
          @update:open="handleDropdownToggle"
        >
          <DropdownMenuTrigger as-child>
            <Button
              variant="ghost"
              size="sm"
              class="h-6 w-6 p-0 hover:scale-110 transition-transform"
            >
              <Icon icon="mdi:dots-horizontal" class="h-3 w-3" />
              <span class="sr-only">更多操作</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" class="w-40">
            <DropdownMenuItem
              v-for="action in actions.dropdown"
              :key="`dropdown-${action.key || action.icon}`"
              :class="cn('cursor-pointer', getDropdownItemClasses(action))"
              @click.stop="handleActionClick(action)"
            >
              <Icon
                v-if="action.icon"
                :icon="action.icon"
                class="mr-2 h-4 w-4"
              />
              {{ action.text || action.tooltip }}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  </TooltipProvider>
</template>

<script setup lang="ts">
defineOptions({
  name: 'CompositeRenderer',
})

import { defineComponent, h } from 'vue'
import { Icon } from '@iconify/vue'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'

// 简单的Avatar组件，用于动态渲染
const SimpleAvatar = defineComponent({
  name: 'SimpleAvatar',
  props: {
    image: String,
    name: String,
    initials: String,
    class: String,
  },
  setup(props) {
    return () =>
      h(Avatar, { class: props.class }, [
        props.image
          ? h(AvatarImage, { src: props.image, alt: props.name })
          : null,
        h(AvatarFallback, {}, props.initials || props.name?.charAt(0) || '?'),
      ])
  },
})
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'
import type { CompositeRendererProps } from './types'
import { useCompositeRenderer } from './useRenderer'

const props = defineProps<CompositeRendererProps>()

// 注册组件以供动态渲染使用
const components = {
  SimpleAvatar,
}

const {
  // 状态
  isHovered,
  isDropdownOpen,
  containerRef,

  // 配置和内容
  config,
  mainContent,
  subContents,
  iconComponent,
  actions,

  // 样式类
  containerClasses,
  actionsClasses,
  subContentClasses,

  // 工具函数
  getButtonVariant,
  getDropdownItemClasses,

  // 事件处理
  handleActionClick,
  handleMouseEnter,
  handleMouseLeave,
  handleDropdownToggle,
} = useCompositeRenderer(props)
</script>
