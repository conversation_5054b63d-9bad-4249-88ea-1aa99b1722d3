/**
 * 复合渲染器逻辑钩子 - 优化版
 */

import { ref, computed, onMounted, watch, shallowReactive } from 'vue'
import { cn } from '@/lib/utils'
import { useAutoRowHeight } from '@/components/data-grid/utils/rowHeightHelpers'
import {
  getTailwindSizeClass,
  getButtonVariant as utilGetButtonVariant,
  getDropdownItemClasses as utilGetDropdownItemClasses,
  formatContent,
  type ButtonVariant,
  type ButtonType,
} from '../../utils'
import type {
  CompositeRendererConfig,
  CompositeRendererProps,
  actionButtonConfig,
} from './types'

// 常量定义
const DEFAULT_ICON_SIZE = 32
const DEFAULT_CONFIG: CompositeRendererConfig = {
  main: { field: 'name' as string },
  subs: { items: [], layout: 'horizontal' as const, separator: '·' },
  showActionsCount: 1,
  enableHover: true,
}

export const useCompositeRenderer = (props: CompositeRendererProps) => {
  // 状态管理 - 使用shallowReactive优化
  const state = shallowReactive({
    isHovered: false,
    isDropdownOpen: false,
  })
  const containerRef = ref<HTMLElement>()

  // 合并配置 - 避免深度嵌套
  const config = computed(() => {
    const merged = { ...DEFAULT_CONFIG, ...props.config }
    merged.subs = { ...DEFAULT_CONFIG.subs, ...props.config?.subs }
    return merged
  })

  // 主内容处理逻辑 - 简化
  const mainContent = computed(() => {
    const mainConfig = config.value.main
    if (!mainConfig)
      return { content: String(props.value || ''), isHtml: false }

    const value = props.row[mainConfig.field || 'name'] || props.value
    return formatContent(value, props.row, mainConfig.formatter)
  })

  // 子内容处理逻辑 - 优化过滤和映射
  const subContents = computed(() => {
    const subsConfig = config.value.subs
    if (!subsConfig?.items?.length) {
      return { items: [], layout: 'horizontal', separator: '·' }
    }

    const items = subsConfig.items
      .filter((item) => !item.condition || item.condition(props.row))
      .map((item) => {
        const value = props.row[item.field]
        if (!value) return null

        const formatted = formatContent(value, props.row, item.formatter)
        return {
          ...item,
          content: formatted.content,
          isHtml: formatted.isHtml,
        }
      })
      .filter(Boolean)

    return {
      items,
      layout: subsConfig.layout || 'horizontal',
      separator: subsConfig.separator || '·',
    }
  })

  // 图标组件配置 - 统一渲染逻辑
  const iconComponent = computed(() => {
    const iconConf = config.value.icon
    if (!iconConf) return null

    const sizeClass = getTailwindSizeClass(iconConf.size || DEFAULT_ICON_SIZE)

    switch (iconConf.type) {
      case 'avatar': {
        const avatarValue = props.row[iconConf.avatarField || 'avatar']
        const name =
          props.row[iconConf.nameField || 'name'] || mainContent.value.content

        // 检查avatarValue是否是有效的图片URL
        const isImageUrl =
          avatarValue &&
          (avatarValue.startsWith('http') ||
            avatarValue.startsWith('/') ||
            avatarValue.includes('.jpg') ||
            avatarValue.includes('.png') ||
            avatarValue.includes('.gif') ||
            avatarValue.includes('.webp'))

        const image = isImageUrl ? avatarValue : undefined
        const displayName = name || avatarValue
        const initials = displayName
          .split(' ')
          .map((word: string) => word.charAt(0))
          .join('')
          .toUpperCase()
          .slice(0, 2)

        return {
          type: 'SimpleAvatar',
          class: cn('flex-shrink-0', sizeClass),
          props: {
            image,
            name: displayName,
            initials,
          },
        }
      }
      case 'image': {
        const src = props.row[iconConf.imageField]
        if (!src) return null

        return {
          type: 'img',
          class: cn('flex-shrink-0 object-cover rounded', sizeClass),
          props: {
            src,
            alt: mainContent.value.content,
          },
        }
      }
      case 'icon': {
        if (!iconConf.icon) return null

        return {
          type: 'Icon',
          class: cn('flex-shrink-0', sizeClass),
          props: {
            icon: iconConf.icon,
          },
        }
      }
      default:
        return null
    }
  })

  // 操作按钮逻辑 - 性能优化
  const actions = computed(() => {
    const actionsConfig = (config.value.actions || []) as actionButtonConfig[]
    if (!actionsConfig.length) {
      return { direct: [], dropdown: [], shouldRender: false }
    }

    const showCount = config.value.showActionsCount || 1
    const visibleActions = actionsConfig.filter(
      (action) => !action.condition || action.condition(props.row)
    )

    const shouldRender =
      visibleActions.length > 0 &&
      (!config.value.enableHover || state.isHovered || state.isDropdownOpen)

    return {
      direct: visibleActions.slice(0, showCount),
      dropdown: visibleActions.slice(showCount),
      shouldRender,
    }
  })

  // 样式类计算 - 优化性能
  const containerClasses = computed(() =>
    cn(
      'flex items-center gap-2 p-1 rounded transition-colors duration-200',
      config.value.enableHover && 'hover:bg-muted/50'
    )
  )

  const actionsClasses = computed(() =>
    cn(
      'flex items-center gap-1 opacity-0 transition-opacity duration-200',
      (actions.value.shouldRender || !config.value.enableHover) && 'opacity-100'
    )
  )

  const subContentClasses = computed(() =>
    cn(
      'text-sm text-muted-foreground mt-0.5',
      subContents.value.layout === 'vertical'
        ? 'flex flex-col space-y-0.5'
        : 'flex items-center'
    )
  )

  // 工具函数
  const getButtonVariant = (action: actionButtonConfig) =>
    utilGetButtonVariant(action.type, action.variant)

  const getDropdownItemClasses = (action: actionButtonConfig) => {
    const variant = getButtonVariant(action)
    return utilGetDropdownItemClasses(variant)
  }

  // 事件处理 - 简化
  const handleActionClick = (action: actionButtonConfig) => {
    action.onClick(props.row)
    // 隐藏按钮区域
    state.isHovered = false
    state.isDropdownOpen = false
  }

  const handleMouseEnter = () => {
    state.isHovered = true
  }

  const handleMouseLeave = () => {
    setTimeout(() => {
      if (!state.isDropdownOpen) {
        state.isHovered = false
      }
    }, 100)
  }

  const handleDropdownToggle = (open: boolean) => {
    state.isDropdownOpen = open
    if (open) state.isHovered = true
  }

  // 使用自动行高调整工具
  const { adjustHeight } = useAutoRowHeight(containerRef, config, {
    addMarkerClass: true,
    minHeight: 40,
    padding: 8,
  })

  // 监听内容变化，自动调整行高
  // 注意：只监听影响布局的内容变化，不监听actions的显示/隐藏状态
  watch([subContents, iconComponent], () => adjustHeight(), {
    deep: true,
  })

  // 单独监听actions配置的变化（不包括shouldRender状态）
  watch(
    () => config.value.actions,
    () => adjustHeight(),
    {
      deep: true,
    }
  )

  // 组件挂载后调整行高
  onMounted(() => adjustHeight())

  return {
    // 状态
    isHovered: computed(() => state.isHovered),
    isDropdownOpen: computed(() => state.isDropdownOpen),
    containerRef,

    // 配置和内容
    config,
    mainContent,
    subContents,
    iconComponent,
    actions,

    // 样式类
    containerClasses,
    actionsClasses,
    subContentClasses,

    // 工具函数
    getButtonVariant,
    getDropdownItemClasses,

    // 事件处理
    handleActionClick,
    handleMouseEnter,
    handleMouseLeave,
    handleDropdownToggle,
  }
}
